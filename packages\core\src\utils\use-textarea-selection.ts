import { TextAreaRef } from "antd/es/input/TextArea";
import React, { useEffect } from "react";

const useTextareaSelection = (ref: React.RefObject<TextAreaRef>) => {
  const [start, setStart] = React.useState(0);
  const [end, setEnd] = React.useState(0);

  const getPosition = () => {
    const textarea = ref.current?.resizableTextArea?.textArea;
    if (textarea) {
      setStart(textarea.selectionStart);
      setEnd(textarea.selectionEnd);
    }
  };

  useEffect(() => {
    const textarea = ref.current?.resizableTextArea?.textArea;
    if (!textarea) {
      return;
    }
    textarea.addEventListener("focus", getPosition);
    textarea.addEventListener("keydown", getPosition);
    textarea.addEventListener("mousedown", getPosition);
    textarea.addEventListener("input", getPosition);
    textarea.addEventListener("paste", getPosition);
    textarea.addEventListener("cut", getPosition);
    textarea.addEventListener("select", getPosition);
    textarea.addEventListener("selectstart", getPosition);

    return () => {
      textarea.removeEventListener("focus", getPosition);
      textarea.removeEventListener("keydown", getPosition);
      textarea.removeEventListener("mousedown", getPosition);
      textarea.removeEventListener("input", getPosition);
      textarea.removeEventListener("paste", getPosition);
      textarea.removeEventListener("cut", getPosition);
      textarea.removeEventListener("select", getPosition);
      textarea.removeEventListener("selectstart", getPosition);
    };
  }, [ref.current?.resizableTextArea?.textArea]);

  return { start, end };
};

export default useTextareaSelection;
