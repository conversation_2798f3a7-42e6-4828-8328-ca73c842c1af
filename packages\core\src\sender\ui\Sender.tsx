import { Flex, Input, Modal } from "antd";
import { TextAreaRef } from "antd/es/input/TextArea";
import React from "react";

import { useCommandRunner, useSubscribeCommand } from "@/command";
import { useActiveAgentCode, useActiveConversationId, useIsLoadingMessage } from "@/core";
import { BuildInCommand } from "@/types";
import { Sender as AntdXSender } from "@ant-design/x";
import { Icon } from "@cscs-agent/icons";

import SenderHeader from "./Header";
import SendButton from "./SendButton";

const confirm = Modal.confirm;

interface SenderProps {}

const Sender: React.FC<SenderProps> = () => {
  const [inputValue, setInputValue] = React.useState("");
  const runner = useCommandRunner();
  const [activeConversationId] = useActiveConversationId();
  const [activeAgentCode] = useActiveAgentCode();
  const [isLoadingMessage] = useIsLoadingMessage();
  const textAreaRef = React.useRef<TextAreaRef>(null);

  useSubscribeCommand(BuildInCommand.InsertTextIntoSender, (params) => {});

  const onSubmit = (message: string) => {
    setInputValue("");
    runner(BuildInCommand.SendMessage, { message, conversationId: activeConversationId, agentCode: activeAgentCode });
  };

  const cancel = () => {
    confirm({
      title: "取消发送",
      content: "确定要取消当前对话吗？",
      onOk() {
        runner(BuildInCommand.CancelChatRequest);
      },
    });
  };

  return (
    <>
      <SenderHeader isHomePage={false} />

      <div className="ag:flex ag:p-3 ag:rounded-xl ag:border ag:border-gray-200 ag:shadow-sm ag:items-end">
        <Input.TextArea ref={textAreaRef} value={inputValue} onChange={(e) => setInputValue(e.target.value)} />
        <SendButton onClick={() => onSubmit(inputValue)} onCancel={cancel} isLoading={isLoadingMessage} />
      </div>
    </>
  );
};

export default Sender;
