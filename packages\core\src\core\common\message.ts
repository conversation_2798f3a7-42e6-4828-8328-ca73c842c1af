import { IMessage, IMessagePackage, MessageStatus, Role } from "@/types";

export class Message implements IMessage {
  id = "";
  content: IMessagePackage[] = [];
  role: Role = Role.AI;
  status: MessageStatus = MessageStatus.Loading;
  agentCode = "";

  constructor(id: string, role: Role, content: IMessagePackage[], status: MessageStatus, agentCode: string) {
    this.id = id;
    this.role = role;
    this.content = content;
    this.status = status;
    this.agentCode = agentCode;
  }
}
