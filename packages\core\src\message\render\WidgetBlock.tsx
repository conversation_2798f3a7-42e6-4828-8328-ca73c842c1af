import { XMLParser, XMLValidator } from "fast-xml-parser";
import { nanoid } from "nanoid";
import React, { useMemo } from "react";

import { useActiveAgentConfig } from "@/core";
import { WidgetConfig } from "@/types";
import { buildInWidgets } from "@/widget";

interface WidgetBlockProps {
  content: string;
}

interface WidgetRenderProps {
  config: WidgetConfig;
}

const WidgetBlock: React.FC<WidgetBlockProps> = (props) => {
  const { content } = props;
  const agentConfig = useActiveAgentConfig();

  const widgets = useMemo(() => {
    const $widgets = agentConfig?.message?.blocks?.widgets ?? [];
    return [...buildInWidgets, ...$widgets];
  }, [agentConfig]);

  const widgetConfigList = useMemo(() => {
    const xmlStr = content;
    const isValid = XMLValidator.validate(xmlStr);
    if (!isValid) {
      console.error("Widget parser error, Invalid XML:", xmlStr);
      return [];
    }

    const parser = new XMLParser();
    const parsedConfig = parser.parse(xmlStr).widget;
    // $widgetConfig 可能是对象或者数组
    if (Array.isArray(parsedConfig)) {
      return parsedConfig;
    }
    if (!parsedConfig) return [];
    return [parsedConfig];
  }, [content]);

  const widgetList = useMemo(() => {
    if (widgetConfigList.length === 0 || widgets.length === 0) return null;

    const $widgetList = widgetConfigList
      .map((i) => {
        const widget = widgets.find((j) => j.code === i.code);
        if (!widget) {
          return false;
        }
        return {
          key: nanoid(),
          ...widget,
          props: {
            ...widget.props,
            ...i.props,
          },
        };
      })
      .filter(Boolean) as (WidgetConfig & { key: string })[];

    if ($widgetList.length === 0) {
      console.error("Widget not found:", widgetConfigList);
      return [];
    }
    return $widgetList;
  }, [widgetConfigList, widgets]);

  return widgetList?.map((i) => <WidgetRender key={i.key} config={i} />);
};

const WidgetRender: React.FC<WidgetRenderProps> = (props) => {
  const { config } = props;

  const Component = useMemo(() => {
    return config.component;
  }, [config]);

  return <Component {...config.props} />;
};

export default WidgetBlock;
