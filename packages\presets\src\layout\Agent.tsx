import React from "react";

import { AgentWelcome } from "@/components";

interface DefaultAgentLayoutProps {
  main: {
    sender: React.ReactNode;
  };
}

const DefaultAgentLayout: React.FC<DefaultAgentLayoutProps> = (props) => {
  const { main } = props;

  return (
    <div className="presets:flex presets:h-full presets:w-full presets:bg-[#FBFCFD]">
      {/* Middle content - adaptive width */}
      <div className="presets:flex-1 presets:h-full presets:flex presets:flex-col">
        <div className="presets:flex-1 presets:overflow-y-auto presets:flex presets:items-center presets:justify-center">
          <AgentWelcome />
        </div>
        <div className="presets:w-[800px] presets:mx-auto presets:pb-4 presets:mb-4">{main.sender}</div>
      </div>
    </div>
  );
};

export default DefaultAgentLayout;
