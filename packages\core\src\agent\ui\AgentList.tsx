import React, { useContext } from "react";
import { useNavigate } from "react-router";

import { AgentChatContext } from "@/core/state/context";
import { useActiveAgentMenuCode, useMessages } from "@/core/state/store";
import { getAssetUrl } from "@/utils/asset";

// Agent module exports
const AgentList: React.FC = () => {
  const { config } = useContext(AgentChatContext);
  const agents = config.agents ?? [];
  const [, setMessages] = useMessages();
  const navigate = useNavigate();
  const defaultLogoUrl = getAssetUrl("/assets/common-agent-logo.png");

  const [activeAgentMenuCode, setActiveAgentMenuCode] = useActiveAgentMenuCode();

  const handleAgentClick = (code: string) => {
    if (activeAgentMenuCode === code) return;
    navigate(`/agent/${code}`);
    setActiveAgentMenuCode(code);
    // TODO 在Chat页面跳转时清空消息
    setMessages([]);
  };

  return (
    <ul className="ag:overflow-y-auto ag:overflow-x-hidden ag:h-full ag:pt-2">
      {agents.map((i) => {
        return (
          <li
            key={i.code}
            className={`ag:flex ag:items-center ag:cursor-pointer ag:p-2 ${
              activeAgentMenuCode === i.code ? "ag:bg-[rgba(237,238,239,1)]" : ""
            }`}
            onClick={() => handleAgentClick(i.code)}
          >
            <i className="ag:mr-2">
              <img src={i.logo ?? defaultLogoUrl} width="16" />
            </i>
            <span className="ag:text-sm ag:text-[rgba(37,45,62,0.85)]">{i.name}</span>
          </li>
        );
      })}
    </ul>
  );
};

export default AgentList;
