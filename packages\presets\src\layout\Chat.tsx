import React, { useCallback, useEffect, useRef } from "react";

import { BuildInCommand, useMessages, useSubscribeCommand } from "@cscs-agent/core";

interface DefaultChatLayoutProps {
  main: {
    navigationBar: React.ReactNode;
    message: React.ReactNode;
    sender: React.ReactNode;
  };
  sidePanel: React.ReactNode;
}

const DefaultChatLayout: React.FC<DefaultChatLayoutProps> = (props) => {
  const { main, sidePanel } = props;
  const messageContainerRef = useRef<HTMLDivElement>(null);
  const [messages] = useMessages();
  const [sidePanelIsOpen, setSidePanelIsOpen] = React.useState(false);
  const scrollTimeoutRef = useRef<number | null>(null);

  // Debounced scroll function
  const debouncedScrollToBottom = useCallback(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = window.setTimeout(() => {
      if (messageContainerRef.current) {
        messageContainerRef.current.scrollTo({
          top: messageContainerRef.current.scrollHeight,
          behavior: "smooth",
        });
      }
    }, 100);
  }, []);

  useSubscribeCommand(BuildInCommand.OpenSidePanel, () => {
    setSidePanelIsOpen(true);
  });

  useSubscribeCommand(BuildInCommand.CloseSidePanel, () => {
    setSidePanelIsOpen(false);
  });

  useEffect(() => {
    debouncedScrollToBottom();
  }, [messages, debouncedScrollToBottom]);

  return (
    <div
      className={`presets:flex presets:h-full presets:w-full ${sidePanelIsOpen ? "presets:bg-[#f8f9fb]" : "presets:bg-white"}`}
    >
      {/* Middle content - adaptive width */}
      <div className={`presets:h-full presets:flex presets:flex-col ${sidePanelIsOpen ? "" : "presets:flex-1"}`}>
        <div>{main.navigationBar}</div>
        <div
          className="presets:flex-1 presets:w-full presets:mx-auto presets:p-4 presets:overflow-y-auto"
          ref={messageContainerRef}
        >
          <div className={`${sidePanelIsOpen ? "presets:w-[600px]" : "presets:w-[800px]"} presets:mx-auto`}>
            {main.message}
          </div>
        </div>
        <div
          className={`presets:pb-4 presets:mb-4  presets:mx-auto ${sidePanelIsOpen ? "presets:w-[600px]" : "presets:w-[800px]"}`}
        >
          {main.sender}
        </div>
      </div>

      {/* Right side panel - toggleable */}
      <div
        className={`presets:h-full presets:border-l presets:border-gray-200 presets:transition-all presets:duration-300 ${sidePanelIsOpen ? "presets:flex-1 presets:overflow-auto" : "presets:w-[0] presets:overflow-hidden"}`}
      >
        {sidePanel}
      </div>
    </div>
  );
};

export default DefaultChatLayout;
