import React, { useMemo } from "react";
import { MarkdownHooks } from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehype<PERSON>ermaid from "rehype-mermaid";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";

interface TextBlockProps {
  content: string;
}

const TextBlock: React.FC<TextBlockProps> = (props) => {
  const { content } = props;

  // 优化markdown闪烁问题
  const markdown = useMemo(() => {
    return (
      <MarkdownHooks rehypePlugins={[rehypeHighlight, rehypeRaw, rehypeMermaid]} remarkPlugins={[remarkGfm]}>
        {content}
      </MarkdownHooks>
    );
  }, [content]);

  return <div className="cscs-agent-text-block ag:break-all ag:leading-[1.6em]">{markdown}</div>;
};

export default TextBlock;
