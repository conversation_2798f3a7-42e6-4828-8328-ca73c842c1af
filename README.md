## 开发指南

### 启动开发环境

```bash
# 安装依赖
pnpm i

# 启动开发环境
pnpm dev
```

### 模板项目安装依赖

```bash
pnpm i -C template/basic --ignore-workspace
```

### 部署

Nginx 配置

```nginx
server {
		listen 5800;
		etag on;
		gzip on;
		charset utf-8;
		root /data/agent/web;

		location / {
			add_header Cache-Control no-cache;

			try_files $uri $uri/ /index.html;
		}

		location /api/ {
			proxy_pass http://************:5801;
			proxy_set_header X-Forwarded-Proto $scheme;
			proxy_set_header X-Real-IP $remote_addr;
		}

		location /cluster-api/ {
			proxy_pass http://************:8002/api/;
			proxy_set_header X-Forwarded-Proto $scheme;
			proxy_set_header X-Real-IP $remote_addr;
		}
}
```

### 版本发布

```bash
# 更新版本号
pnpm run build

# 发布
pnpm publish --filter "@cscs-agent/*"
```

## Todo List

- [x] 会话列表的分组功能
- [ ] 会话列表的搜索功能
- [x] 会话列表的排序
- [ ] 会话接口异常处理
- [ ] 代码显示框样式优化
- [ ] message slot
- [ ] sender slot
- [ ] side panel slot
- [ ] 动态页面预览页面退出登录问题
- [ ] 思维链支持
- [ ] StructuredMessagePackage 支持
- [ ] 富文本输入框
- [ ] 替换 sender

