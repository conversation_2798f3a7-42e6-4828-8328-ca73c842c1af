import React from "react";

import { IMessagePackage } from "@/types";

import PackageRender from "./PackageRender";

interface MessageRenderProps {
  packages: IMessagePackage[];
}

/**
 * 解析消息内容
 * 首先依次解析每个 Package，
 */
const MessageRender: React.FC<MessageRenderProps> = (props) => {
  const { packages } = props;

  return (
    <div className="ag:text-sm ag:text-[rgba(37,45,62,0.85)]">
      {packages.map((i) => (
        <PackageRender key={i.package_id} msgPackage={i} />
      ))}
    </div>
  );
};

export default MessageRender;
