import { dirname, resolve } from "path";
import { fileURLToPath } from "url";

import { defineConfig } from "vite";
import dts from "vite-plugin-dts";
import svgr from "vite-plugin-svgr";

import tailwindcss from "@tailwindcss/vite";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    tailwindcss(),
    dts({
      tsconfigPath: resolve(__dirname, mode === "development" ? "tsconfig.json" : "tsconfig.build.json"),
      exclude: ["**/__tests__/**", "**/mock/**"],
    }),
    svgr({
      include: "**/*.svg?react",
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, "src/index.ts"),
      name: "@cscs-agent/core",
      fileName: (format) => `index.${format}.js`,
      formats: ["es", "cjs"],
    },
    sourcemap: mode === "development",
    outDir: "dist",
    cssCodeSplit: true,
    cssTarget: "es2020",
    cssMinify: true,
    rollupOptions: {
      // Make sure to externalize deps that shouldn't be bundled
      // Dependencies from package.json
      external: [
        // Dependencies
        "@cscs-agent/icons",
        "rxjs",
        "nanoid",
        "penpal",
        "@ant-design/x",
        "fast-xml-parser",
        "eventemitter3",
        "jotai",
        "react-infinite-scroll-component",
        "react-markdown",
        "rehype-highlight",
        "rehype-raw",
        "remark-gfm",
        "rehype-mermaid",
        // Peer dependencies
        "@ant-design/icons",
        "antd",
        "antd-style",
        "react",
        "react-dom",
        "react-router",
        // Other
        "react/jsx-runtime",
      ],
      output: {
        // Provide global variables to use in the UMD build
        globals: {
          // Dependencies
          rxjs: "rxjs",
          nanoid: "nanoid",
          penpal: "penpal",
          "@ant-design/x": "AntDesignX",
          "fast-xml-parser": "FastXmlParser",
          "eventemitter3": "EventEmitter3",
          jotai: "Jotai",
          "react-infinite-scroll-component": "ReactInfiniteScrollComponent",
          "react-markdown": "ReactMarkdown",
          "rehype-highlight": "RehypeHighlight",
          "rehype-raw": "RehypeRaw",
          "remark-gfm": "RemarkGfm",
          "rehype-mermaid": "RehypeMermaid",
          // Peer dependencies
          "@ant-design/icons": "AntDesignIcons",
          antd: "antd",
          "antd-style": "antdStyle",
          react: "React",
          "react-dom": "ReactDOM",
          "react-router": "ReactRouter",
          // Other
          "react/jsx-runtime": "ReactJSXRuntime",
        },
        assetFileNames: "agent-tailwind.css",
      },
    },
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
    devSourcemap: true,
  },
}));
