import { Flex } from "antd";
import React from "react";

import { useCommandRunner } from "@/command";
import { useActiveAgentCode, useActiveAgentMenuCode } from "@/core";
import { BuildInCommand } from "@/types";
import { Sender as AntdXSender } from "@ant-design/x";
import { Icon } from "@cscs-agent/icons";

import SenderHeader from "./Header";

interface AgentHomeSenderProps {}

const AgentHomeSender: React.FC<AgentHomeSenderProps> = () => {
  const [inputValue, setInputValue] = React.useState("");
  const [loading] = React.useState(false);
  const runner = useCommandRunner();
  const [activeAgentMenuCode] = useActiveAgentMenuCode();
  const [, setActiveAgentCode] = useActiveAgentCode();

  const onSubmit = (message: string) => {
    runner(BuildInCommand.SendMessage, { message, agentCode: activeAgentMenuCode, isNewConversation: true });
    setActiveAgentCode(activeAgentMenuCode);
  };

  return (
    <>
      <SenderHeader isHomePage={true} />
      <AntdXSender
        value={inputValue}
        onSubmit={(message) => {
          onSubmit(message);
          setInputValue("");
        }}
        onChange={setInputValue}
        onCancel={() => {
          runner(BuildInCommand.CancelChatRequest, {});
        }}
        loading={loading}
        actions={(_, info) => {
          const { SendButton, LoadingButton } = info.components;
          return (
            <Flex gap={4}>
              {loading ? (
                <LoadingButton type="default" icon={<Icon icon="ArrowUp" style={{ color: "#ffffff" }} />} />
              ) : (
                <SendButton type="primary" icon={<Icon icon="ArrowUp" style={{ color: "#ffffff" }} />} />
              )}
            </Flex>
          );
        }}
        placeholder="发消息"
      />
    </>
  );
};

export default AgentHomeSender;
